document.addEventListener('DOMContentLoaded', function () {

    /**
     * PostModalController
     * 统一管理新建和编辑笔记的模态框
     */
    class PostModalController {
        constructor(options) {
            this.options = options;
            this.modal = document.getElementById('postModal');
            this.form = document.getElementById('postForm');
            this.title = document.getElementById('modalTitle');
            this.modalIcon = document.getElementById('modalIcon');
            this.submitButton = document.getElementById('submitButton');
            this.editor = document.getElementById('modalMarkdownEditor');
            this.postIdField = document.getElementById('postIdField');
            this.editTimeWrapper = document.getElementById('editTimeWrapper');
            this.postTimeInput = document.getElementById('postTimeInput');
            this.modeIndicator = document.getElementById('modeIndicator');

            this.tagManager = new TagManager({
                tagInput: document.getElementById('modalTagInput'),
                tagsContainer: document.getElementById('modalTagsContainer'),
                tagsField: document.getElementById('modalTagsField')
            });

            this.bindEvents();
        }

        bindEvents() {
            // 关闭模态框
            this.modal.querySelectorAll('[data-dismiss="modal"]').forEach(el => {
                el.addEventListener('click', () => this.hide());
            });

            // 表单提交
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));

            // 自动保存草稿
            this.editor.addEventListener('input', () => this.saveDraft());
            this.tagManager.tagsContainer.addEventListener('tagsUpdated', () => this.saveDraft());

            // 设置当前时间
            document.getElementById('setCurrentTimeBtn').addEventListener('click', () => this.setCurrentTime());

            // 快捷键支持
            document.addEventListener('keydown', (e) => this.handleKeydown(e));
        }

        setCurrentTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            this.postTimeInput.value = `${year}-${month}-${day} ${hours}:${minutes}`;
        }

        setModalIcon(mode) {
            if (!this.modalIcon) return;

            const icons = {
                create: `<svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>`,
                edit: `<svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>`
            };

            this.modalIcon.innerHTML = icons[mode] || icons.create;

            // 更新图标颜色
            if (mode === 'edit') {
                this.modalIcon.className = 'w-5 h-5 text-amber-600';
            } else {
                this.modalIcon.className = 'w-5 h-5 text-indigo-600';
            }
        }

        setSubmitButtonStyle(mode) {
            if (!this.submitButton) return;

            if (mode === 'edit') {
                this.submitButton.className = 'p-2 text-amber-600 hover:text-amber-800 transition-colors duration-200';
                this.submitButton.title = '保存更改';
            } else {
                this.submitButton.className = 'p-2 text-indigo-600 hover:text-indigo-800 transition-colors duration-200';
                this.submitButton.title = '发布笔记';
            }
        }

        showLoadingState(message = '加载中...') {
            // 禁用提交按钮
            this.submitButton.disabled = true;
            this.submitButton.style.opacity = '0.5';

            // 显示加载消息
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'modalLoadingState';
            loadingDiv.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            loadingDiv.innerHTML = `
                <div class="bg-white rounded-lg p-6 flex items-center gap-3 shadow-xl">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"></div>
                    <span class="text-gray-700">${message}</span>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }

        hideLoadingState() {
            // 启用提交按钮
            this.submitButton.disabled = false;
            this.submitButton.style.opacity = '1';

            // 移除加载状态
            const loadingDiv = document.getElementById('modalLoadingState');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        showError(message) {
            // 创建错误提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
            errorDiv.innerHTML = `
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm">${message}</span>
                    <button class="ml-2 text-red-500 hover:text-red-700" onclick="this.parentElement.parentElement.remove()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            document.body.appendChild(errorDiv);

            // 自动移除错误提示
            setTimeout(() => {
                if (errorDiv.parentElement) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        showSuccess(message) {
            // 创建成功提示
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
            successDiv.innerHTML = `
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm">${message}</span>
                    <button class="ml-2 text-green-500 hover:text-green-700" onclick="this.parentElement.parentElement.remove()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            document.body.appendChild(successDiv);

            // 自动移除成功提示
            setTimeout(() => {
                if (successDiv.parentElement) {
                    successDiv.remove();
                }
            }, 3000);
        }

        handleKeydown(e) {
            // 只在模态框显示时处理快捷键
            if (!this.modal.classList.contains('show')) return;

            // Ctrl/Cmd + Enter: 提交表单
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.form.dispatchEvent(new Event('submit'));
            }

            // Escape: 关闭模态框
            if (e.key === 'Escape') {
                e.preventDefault();
                this.hide();
            }

            // Ctrl/Cmd + S: 保存（阻止浏览器默认保存行为）
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.form.dispatchEvent(new Event('submit'));
            }
        }

        saveDraft() {
            // 只在新建模式下保存草稿
            if (this.postIdField.value) return;

            const draft = {
                content: this.editor.value,
                tags: this.tagManager.getTags(),
                timestamp: Date.now()
            };

            // 只有内容不为空时才保存
            if (draft.content.trim() || draft.tags.length > 0) {
                localStorage.setItem('noteDraft', JSON.stringify(draft));
            }
        }

        loadDraft() {
            try {
                const draftStr = localStorage.getItem('noteDraft');
                if (!draftStr) return null;

                const draft = JSON.parse(draftStr);
                // 检查草稿是否过期（24小时）
                if (Date.now() - draft.timestamp > 24 * 60 * 60 * 1000) {
                    localStorage.removeItem('noteDraft');
                    return null;
                }

                return draft;
            } catch (error) {
                console.error('Failed to load draft:', error);
                localStorage.removeItem('noteDraft');
                return null;
            }
        }

        clearDraft() {
            localStorage.removeItem('noteDraft');
        }

        showDraftRestoreOption(draft) {
            const draftDiv = document.createElement('div');
            draftDiv.id = 'draftRestoreOption';
            draftDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4';
            draftDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-800">发现未保存的草稿</p>
                            <p class="text-xs text-blue-600">保存时间: ${new Date(draft.timestamp).toLocaleString()}</p>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <button type="button" class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors" onclick="window.postModalController.restoreDraft()">恢复</button>
                        <button type="button" class="px-3 py-1 text-xs bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors" onclick="window.postModalController.discardDraft()">丢弃</button>
                    </div>
                </div>
            `;

            // 插入到编辑器前面
            const editorContainer = this.modal.querySelector('.editor-container').parentElement;
            editorContainer.parentElement.insertBefore(draftDiv, editorContainer);
        }

        restoreDraft() {
            const draft = this.loadDraft();
            if (draft) {
                this.editor.value = draft.content;
                this.tagManager.setTags(draft.tags);
            }
            this.removeDraftRestoreOption();
        }

        discardDraft() {
            this.clearDraft();
            this.removeDraftRestoreOption();
        }

        removeDraftRestoreOption() {
            const draftDiv = document.getElementById('draftRestoreOption');
            if (draftDiv) {
                draftDiv.remove();
            }
        }

        resetForm() {
            this.form.reset();
            this.tagManager.clearTags(false);
            this.postIdField.value = '';
            this.editor.value = '';
        }

        show() {
            document.body.style.overflow = 'hidden';
            this.modal.classList.add('show');
        }

        hide() {
            document.body.style.overflow = '';
            this.modal.classList.remove('show');
            this.resetForm();
        }

        openForCreate() {
            this.resetForm();
            this.title.textContent = '发布笔记';
            this.setModalIcon('create');
            this.setSubmitButtonStyle('create');
            this.editTimeWrapper.classList.add('hidden');
            this.modeIndicator.classList.add('hidden');

            // 检查是否有草稿
            const draft = this.loadDraft();
            if (draft) {
                this.showDraftRestoreOption(draft);
            }

            this.show();
        }

        openForEdit(postId) {
            this.resetForm();
            this.showLoadingState('正在加载笔记...');

            fetch(`/api/post/${postId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const post = data.post;
                        this.title.textContent = '编辑笔记';
                        this.setModalIcon('edit');
                        this.setSubmitButtonStyle('edit');
                        this.editor.value = post.content;
                        this.postIdField.value = post.id;
                        this.tagManager.setTags(post.tags || []);

                        // 设置和显示发布时间
                        this.postTimeInput.value = post.created_at;
                        this.editTimeWrapper.classList.remove('hidden');
                        this.modeIndicator.classList.remove('hidden');

                        this.hideLoadingState();
                        this.show();
                    } else {
                        this.hideLoadingState();
                        this.showError('加载笔记失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.hideLoadingState();
                    this.showError('加载笔记失败，请检查网络连接后重试');
                });
        }

        handleSubmit(e) {
            e.preventDefault();

            // 防止重复提交
            if (this.submitButton.disabled) return;

            this.tagManager.processRemainingTag();

            // 检查用户设置的时间是否大于当前时间
            const userTime = this.postTimeInput.value;
            if (userTime) {
                const userDateTime = new Date(userTime);
                const currentDateTime = new Date();
                
                if (userDateTime > currentDateTime) {
                    const useCurrentTime = confirm('您设置的时间大于当前时间，是否使用当前时间？\n\n点击"确定"使用当前时间\n点击"取消"保持您设置的时间');
                    
                    if (useCurrentTime) {
                        this.setCurrentTime();
                    }
                }
            }

            const postId = this.postIdField.value;
            const isEditing = !!postId;
            const url = isEditing ? `/post/${postId}/update` : '/post/create';

            // 显示提交状态
            this.showLoadingState(isEditing ? '正在保存更改...' : '正在发布笔记...');

            const formData = new FormData(this.form);
            // FormData 会自动处理 input 的值，但我们需要确保 tags 是 JSON 字符串
            formData.set('tags', JSON.stringify(this.tagManager.getTags()));
            
            // 手动添加发布时间字段（因为它在表单外部）
            if (this.postTimeInput.value) {
                formData.set('created_at', this.postTimeInput.value);
            }

            fetch(url, {
                method: 'POST',
                body: new URLSearchParams(formData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                this.hideLoadingState();
                if (data.success) {
                    // 清除草稿（仅新建时）
                    if (!isEditing) {
                        this.clearDraft();
                    }
                    this.hide();
                    this.showSuccess(isEditing ? '笔记更新成功！' : '笔记发布成功！');
                    if (isEditing) {
                        // 检查是否在随机页面
                        if (window.location.pathname === '/random') {
                            // 调用随机页面专用的更新函数
                            if (typeof window.updatePostInRandomDOM === 'function') {
                                window.updatePostInRandomDOM(data.post);
                            }
                        } else {
                            // 首页的更新函数
                            this.updatePostInDOM(data.post);
                        }
                    } else {
                        // 检查是否在随机页面
                        if (window.location.pathname === '/random') {
                            // 在随机页面发布新笔记后，更新页面显示新笔记
                            if (typeof window.updateRandomPostDisplay === 'function') {
                                // 更新随机页面的显示区域
                                const postDisplayArea = document.getElementById('post-display-area');
                                if (postDisplayArea) {
                                    // 创建新的笔记HTML结构
                                    const newPostHtml = `
                                        <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in md:p-6" data-post-id="${data.post.id}">
                                            <div>
                                                <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                                                    <span class="text-sm text-gray-500 font-medium">${data.post.created_at}</span>
                                                    <div class="flex gap-2">
                                                        <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post" data-post-id="${data.post.id}">编辑</button>
                                                        <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post" data-post-id="${data.post.id}">删除</button>
                                                    </div>
                                                </div>
                                                <div class="post-content leading-relaxed text-gray-800 mb-8 break-words markdown-content" id="content-${data.post.id}">${data.post.rendered_content}</div>
                                            </div>
                                            ${data.post.tags && data.post.tags.length > 0 ? 
                                            `<div class="tags-container mt-6 pt-4 border-t border-gray-200 flex flex-wrap gap-2">
                                                ${data.post.tags.map(tag => `
                                                    <a href="/?tag=${encodeURIComponent(tag)}"
                                                       class="note-tag note-tag--normal">
                                                        <span class="tag-text">${tag}</span>
                                                    </a>
                                                `).join('')}
                                            </div>` : ''}
                                        </div>
                                    `;
                                    postDisplayArea.innerHTML = newPostHtml;
                                }
                            }
                        } else {
                            window.location.reload(); // 首页新建后刷新页面以保证排序正确
                        }
                    }
                } else {
                    this.showError('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.hideLoadingState();
                this.showError('操作失败，请检查网络连接后重试');
            });
        }

        updatePostInDOM(post) {
            const postCard = document.querySelector(`.card[data-post-id="${post.id}"]`);
            if (postCard) {
                // 更新内容
                const contentElement = postCard.querySelector('.post-content');
                if (contentElement) {
                    contentElement.innerHTML = post.rendered_content;
                }
                
                // 更新时间
                const timeElement = postCard.querySelector('.post-time');
                if (timeElement) {
                    timeElement.textContent = post.created_at;
                }
                
                // 更新标签容器
                const tagsContainer = postCard.querySelector('.tags-container');
                if (tagsContainer) {
                    tagsContainer.innerHTML = '';
                    if (post.tags && post.tags.length > 0) {
                        post.tags.forEach(tag => {
                            const tagLink = document.createElement('a');
                            tagLink.href = `/?tag=${encodeURIComponent(tag)}`;
                            tagLink.className = 'note-tag note-tag--normal';
                            tagLink.innerHTML = `<span class="tag-text">${tag}</span>`;
                            tagsContainer.appendChild(tagLink);
                        });
                    }
                }
                
                // 确保编辑和删除按钮仍然有正确的数据属性
                const editButton = postCard.querySelector('.edit-post');
                const deleteButton = postCard.querySelector('.delete-post');
                if (editButton) {
                    editButton.setAttribute('data-post-id', post.id);
                }
                if (deleteButton) {
                    deleteButton.setAttribute('data-post-id', post.id);
                }
            }
        }
    }

    

    /**
     * DeleteController
     * 管理删除确认模态框
     */
    class DeleteController {
        constructor() {
            this.modal = document.getElementById('deleteConfirmModal');
            this.confirmButton = this.modal.querySelector('.btn-danger');
            this.currentPostId = null;
            this.bindEvents();
        }

        bindEvents() {
            this.modal.querySelectorAll('[data-dismiss="modal"]').forEach(el => {
                el.addEventListener('click', () => this.hide());
            });
            this.confirmButton.addEventListener('click', (e) => this.handleDelete(e));
        }

        show(postId) {
            this.currentPostId = postId;
            this.modal.classList.add('show');
        }

        hide() {
            this.currentPostId = null;
            this.modal.classList.remove('show');
        }

        handleDelete(e) {
            e.preventDefault();
            if (!this.currentPostId) return;

            fetch(`/post/${this.currentPostId}/delete`, { method: 'POST' })
                .then(response => {
                    if (response.ok) {
                        document.querySelector(`.card[data-post-id="${this.currentPostId}"]`).remove();
                        this.hide();
                    } else {
                        throw new Error('删除失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败，请重试');
                    this.hide();
                });
        }
    }

    // 初始化所有控制器
    const postModalController = new PostModalController();
    const deleteController = new DeleteController();

    // 暴露控制器实例供全局使用
    window.postModalController = postModalController;

    // 暴露全局函数供随机页面调用
    window.openPostModal = function() {
        postModalController.openForCreate();
    };

    // 暴露全局函数供随机页面更新笔记显示
    window.updateRandomPostDisplay = function(postData) {
        postModalController.updatePostInDOM(postData);
    };

    // 全局事件委托
    document.addEventListener('click', function (event) {
        const target = event.target;

        // 新建笔记
        if (target.closest('#postButton')) {
            postModalController.openForCreate();
        }

        // 编辑笔记
        if (target.classList.contains('edit-post')) {
            const postId = target.getAttribute('data-post-id');
            postModalController.openForEdit(postId);
        }

        // 删除笔记
        if (target.classList.contains('delete-post')) {
            const postId = target.getAttribute('data-post-id');
            deleteController.show(postId);
        }
    });
});
